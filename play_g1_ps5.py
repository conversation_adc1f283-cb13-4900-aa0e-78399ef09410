#!/usr/bin/env python3
"""Deploy a G1 MJX policy in ONNX format to C MuJoCo and control with PS5 controller."""

import sys
from etils import epath
import mujoco
import mujoco.viewer as viewer
import numpy as np
import onnxruntime as rt

# Add current directory to Python path for imports
sys.path.insert(0, str(epath.Path(__file__).parent))

# Now we can import the G1 assets function
from mujoco_playground._src.locomotion.g1.base import get_assets

_HERE = epath.Path(__file__).parent
_G1_XML = _HERE / "mujoco_playground" / "_src" / "locomotion" / "g1" / "xmls" / "scene_mjx_feetonly_flat_terrain.xml"
_ONNX_DIR = _HERE / "mujoco_playground" / "experimental" / "sim2sim" / "onnx"


class OnnxController:
  """ONNX controller for the G1 robot with PS5 controller support."""

  def __init__(
      self,
      policy_path: str,
      default_angles: np.ndarray,
      ctrl_dt: float,
      n_substeps: int,
      action_scale: float = 0.5,
      vel_scale_x: float = 1.0,
      vel_scale_y: float = 1.0,
      vel_scale_rot: float = 1.0,
  ):
    self._output_names = ["continuous_actions"]
    self._policy = rt.InferenceSession(
        policy_path, providers=["CPUExecutionProvider"]
    )

    self._action_scale = action_scale
    self._default_angles = default_angles
    self._last_action = np.zeros_like(default_angles, dtype=np.float32)

    self._counter = 0
    self._n_substeps = n_substeps

    self._phase = np.array([0.0, np.pi])
    self._gait_freq = 1.5
    self._phase_dt = 2 * np.pi * self._gait_freq * ctrl_dt

    # Store controller parameters for later initialization
    self._vel_scale_x = vel_scale_x
    self._vel_scale_y = vel_scale_y
    self._vel_scale_rot = vel_scale_rot

    # Initialize PS5 controller (will be done later to avoid crashes)
    self._ps5_controller = None
    self._use_controller = False
    self._controller_init_attempted = False

  def _try_init_controller(self):
    """Try to initialize PS5 controller if not already attempted."""
    if not self._controller_init_attempted:
        self._controller_init_attempted = True
        try:
            from ps5_controller import PS5Controller
            self._ps5_controller = PS5Controller(
                vel_scale_x=self._vel_scale_x,
                vel_scale_y=self._vel_scale_y,
                vel_scale_rot=self._vel_scale_rot,
                deadzone=0.1,
            )
            self._use_controller = True
            print("🎮 PS5 Controller initialized!")
            print("📋 Controls:")
            print("   Left Stick: Forward/Back + Strafe")
            print("   Right Stick: Turn Left/Right")
        except Exception as e:
            print(f"⚠️  PS5 Controller not available: {e}")
            print("🤖 Using autonomous walking mode")
            self._use_controller = False
            self._ps5_controller = None

  def get_obs(self, model, data) -> np.ndarray:
    # Try to initialize controller on first call
    self._try_init_controller()

    linvel = data.sensor("local_linvel").data
    gyro = data.sensor("gyro").data
    imu_xmat = data.site_xmat[model.site("imu").id].reshape(3, 3)
    gravity = imu_xmat.T @ np.array([0, 0, -1])
    joint_angles = data.qpos[7:] - self._default_angles
    joint_velocities = data.qvel[6:]
    phase = np.concatenate([np.cos(self._phase), np.sin(self._phase)])

    # Get command from PS5 controller or use autonomous mode
    if self._use_controller and self._ps5_controller and self._ps5_controller.is_connected():
        command = self._ps5_controller.get_command()
    else:
        # Autonomous walking mode - walk forward slowly
        command = np.array([0.3, 0.0, 0.0])  # [forward_vel, side_vel, turn_vel]

    obs = np.hstack([
        linvel,
        gyro,
        gravity,
        command,
        joint_angles,
        joint_velocities,
        self._last_action,
        phase,
    ])
    return obs.astype(np.float32)

  def get_control(self, model: mujoco.MjModel, data: mujoco.MjData) -> None:
    self._counter += 1
    if self._counter % self._n_substeps == 0:
      obs = self.get_obs(model, data)
      onnx_input = {"obs": obs.reshape(1, -1)}
      onnx_pred = self._policy.run(self._output_names, onnx_input)[0][0]
      self._last_action = onnx_pred.copy()
      data.ctrl[:] = onnx_pred * self._action_scale + self._default_angles
      phase_tp1 = self._phase + self._phase_dt
      self._phase = np.fmod(phase_tp1 + np.pi, 2 * np.pi) - np.pi

  def cleanup(self):
    """Clean up controller resources."""
    if self._ps5_controller:
        self._ps5_controller.stop()


def load_callback(model=None, data=None):
  mujoco.set_mjcb_control(None)

  model = mujoco.MjModel.from_xml_path(
      _G1_XML.as_posix(),
      assets=get_assets()
  )
  data = mujoco.MjData(model)

  mujoco.mj_resetDataKeyframe(model, data, 0)

  ctrl_dt = 0.02
  sim_dt = 0.005
  n_substeps = int(round(ctrl_dt / sim_dt))
  model.opt.timestep = sim_dt

  global policy_controller
  policy_controller = OnnxController(
      policy_path=(_ONNX_DIR / "g1_policy.onnx").as_posix(),
      default_angles=np.array(model.keyframe("knees_bent").qpos[7:]),
      ctrl_dt=ctrl_dt,
      n_substeps=n_substeps,
      action_scale=0.5,
      vel_scale_x=1.5,
      vel_scale_y=0.8,
      vel_scale_rot=1.5,
  )

  mujoco.set_mjcb_control(policy_controller.get_control)

  return model, data


if __name__ == "__main__":
  print("🤖 G1 Robot with PS5 Controller Support")
  print("=" * 50)
  print("📋 Setup Instructions:")
  print("1. Connect your PS5 DualSense controller via USB or Bluetooth")
  print("2. On macOS: Go to System Preferences > Bluetooth and pair the controller")
  print("3. The controller should appear as 'DualSense Wireless Controller'")
  print("4. If no controller is detected, the robot will walk autonomously")
  print("=" * 50)
  
  try:
    viewer.launch(loader=load_callback)
  except KeyboardInterrupt:
    print("\n👋 Shutting down...")
  finally:
    # Clean up controller resources
    if 'policy_controller' in globals():
        policy_controller.cleanup()

#!/usr/bin/env python3
"""Deploy a G1 MJX policy in ONNX format to C MuJoCo with keyboard control."""

import sys
import threading
import time
from etils import epath
import mujoco
import mujoco.viewer as viewer
import numpy as np
import onnxruntime as rt

# Add current directory to Python path for imports
sys.path.insert(0, str(epath.Path(__file__).parent))

# Import the G1 assets function
from mujoco_playground._src.locomotion.g1.base import get_assets

_HERE = epath.Path(__file__).parent
_G1_XML = _HERE / "mujoco_playground" / "_src" / "locomotion" / "g1" / "xmls" / "scene_mjx_feetonly_flat_terrain.xml"
_ONNX_DIR = _HERE / "mujoco_playground" / "experimental" / "sim2sim" / "onnx"


class KeyboardController:
    """Simple keyboard controller for robot movement."""
    
    def __init__(self, vel_scale_x=1.0, vel_scale_y=1.0, vel_scale_rot=1.0):
        self.vel_scale_x = vel_scale_x
        self.vel_scale_y = vel_scale_y
        self.vel_scale_rot = vel_scale_rot
        
        # Current command
        self.vx = 0.0
        self.vy = 0.0
        self.wz = 0.0
        
        # Key states
        self.keys_pressed = set()
        self.running = True
        
        # Start keyboard listener thread
        self.keyboard_thread = threading.Thread(target=self._keyboard_listener, daemon=True)
        self.keyboard_thread.start()
        
        print("⌨️  Keyboard Controls:")
        print("   W/S: Forward/Backward")
        print("   A/D: Strafe Left/Right") 
        print("   Q/E: Turn Left/Right")
        print("   Space: Stop")
        print("   ESC: Exit")
    
    def _keyboard_listener(self):
        """Listen for keyboard input (simplified version)."""
        try:
            import termios
            import tty
            import select
            
            # Save terminal settings
            old_settings = termios.tcgetattr(sys.stdin)
            tty.setraw(sys.stdin.fileno())
            
            while self.running:
                if select.select([sys.stdin], [], [], 0.1)[0]:
                    key = sys.stdin.read(1)
                    self._process_key(key)
                
                self._update_command()
                time.sleep(0.05)
                
        except ImportError:
            print("⚠️  Advanced keyboard control not available on this system")
            print("🤖 Using autonomous walking mode")
        except Exception as e:
            print(f"Keyboard listener error: {e}")
        finally:
            try:
                termios.tcsetattr(sys.stdin, termios.TCSADRAIN, old_settings)
            except:
                pass
    
    def _process_key(self, key):
        """Process a single key press."""
        key = key.lower()
        
        if key == 'w':
            self.keys_pressed.add('forward')
        elif key == 's':
            self.keys_pressed.add('backward')
        elif key == 'a':
            self.keys_pressed.add('left')
        elif key == 'd':
            self.keys_pressed.add('right')
        elif key == 'q':
            self.keys_pressed.add('turn_left')
        elif key == 'e':
            self.keys_pressed.add('turn_right')
        elif key == ' ':
            self.keys_pressed.clear()
        elif key == '\x1b':  # ESC
            self.running = False
    
    def _update_command(self):
        """Update movement command based on pressed keys."""
        self.vx = 0.0
        self.vy = 0.0
        self.wz = 0.0
        
        if 'forward' in self.keys_pressed:
            self.vx += self.vel_scale_x
        if 'backward' in self.keys_pressed:
            self.vx -= self.vel_scale_x
        if 'left' in self.keys_pressed:
            self.vy += self.vel_scale_y
        if 'right' in self.keys_pressed:
            self.vy -= self.vel_scale_y
        if 'turn_left' in self.keys_pressed:
            self.wz += self.vel_scale_rot
        if 'turn_right' in self.keys_pressed:
            self.wz -= self.vel_scale_rot
    
    def get_command(self):
        """Get current movement command."""
        return np.array([self.vx, self.vy, self.wz], dtype=np.float32)
    
    def stop(self):
        """Stop the keyboard controller."""
        self.running = False


class OnnxController:
  """ONNX controller for the G1 robot with keyboard control."""

  def __init__(
      self,
      policy_path: str,
      default_angles: np.ndarray,
      ctrl_dt: float,
      n_substeps: int,
      action_scale: float = 0.5,
      vel_scale_x: float = 1.0,
      vel_scale_y: float = 1.0,
      vel_scale_rot: float = 1.0,
  ):
    self._output_names = ["continuous_actions"]
    self._policy = rt.InferenceSession(
        policy_path, providers=["CPUExecutionProvider"]
    )

    self._action_scale = action_scale
    self._default_angles = default_angles
    self._last_action = np.zeros_like(default_angles, dtype=np.float32)

    self._counter = 0
    self._n_substeps = n_substeps

    self._phase = np.array([0.0, np.pi])
    self._gait_freq = 1.5
    self._phase_dt = 2 * np.pi * self._gait_freq * ctrl_dt

    # Initialize keyboard controller
    try:
        self._keyboard_controller = KeyboardController(
            vel_scale_x=vel_scale_x,
            vel_scale_y=vel_scale_y,
            vel_scale_rot=vel_scale_rot
        )
        self._use_keyboard = True
    except Exception as e:
        print(f"⚠️  Keyboard control not available: {e}")
        print("🤖 Using autonomous walking mode")
        self._use_keyboard = False
        self._keyboard_controller = None

  def get_obs(self, model, data) -> np.ndarray:
    linvel = data.sensor("local_linvel_pelvis").data
    gyro = data.sensor("gyro_pelvis").data
    imu_xmat = data.site_xmat[model.site("imu_in_pelvis").id].reshape(3, 3)
    gravity = imu_xmat.T @ np.array([0, 0, -1])
    joint_angles = data.qpos[7:] - self._default_angles
    joint_velocities = data.qvel[6:]
    phase = np.concatenate([np.cos(self._phase), np.sin(self._phase)])
    
    # Get command from keyboard or use autonomous mode
    if self._use_keyboard and self._keyboard_controller:
        command = self._keyboard_controller.get_command()
        # If no input, use slow forward walk
        if np.allclose(command, 0.0):
            command = np.array([0.3, 0.0, 0.0])
    else:
        # Autonomous walking mode - walk forward slowly
        command = np.array([0.3, 0.0, 0.0])  # [forward_vel, side_vel, turn_vel]
    
    obs = np.hstack([
        linvel,
        gyro,
        gravity,
        command,
        joint_angles,
        joint_velocities,
        self._last_action,
        phase,
    ])
    return obs.astype(np.float32)

  def get_control(self, model: mujoco.MjModel, data: mujoco.MjData) -> None:
    self._counter += 1
    if self._counter % self._n_substeps == 0:
      obs = self.get_obs(model, data)
      onnx_input = {"obs": obs.reshape(1, -1)}
      onnx_pred = self._policy.run(self._output_names, onnx_input)[0][0]
      self._last_action = onnx_pred.copy()
      data.ctrl[:] = onnx_pred * self._action_scale + self._default_angles
      phase_tp1 = self._phase + self._phase_dt
      self._phase = np.fmod(phase_tp1 + np.pi, 2 * np.pi) - np.pi

  def cleanup(self):
    """Clean up controller resources."""
    if self._keyboard_controller:
        self._keyboard_controller.stop()


def load_callback(model=None, data=None):
  mujoco.set_mjcb_control(None)

  model = mujoco.MjModel.from_xml_path(
      _G1_XML.as_posix(),
      assets=get_assets()
  )
  data = mujoco.MjData(model)

  mujoco.mj_resetDataKeyframe(model, data, 0)

  ctrl_dt = 0.02
  sim_dt = 0.005
  n_substeps = int(round(ctrl_dt / sim_dt))
  model.opt.timestep = sim_dt

  global policy_controller
  policy_controller = OnnxController(
      policy_path=(_ONNX_DIR / "g1_policy.onnx").as_posix(),
      default_angles=np.array(model.keyframe("knees_bent").qpos[7:]),
      ctrl_dt=ctrl_dt,
      n_substeps=n_substeps,
      action_scale=0.5,
      vel_scale_x=1.5,
      vel_scale_y=0.8,
      vel_scale_rot=1.5,
  )

  mujoco.set_mjcb_control(policy_controller.get_control)

  return model, data


if __name__ == "__main__":
  print("🤖 G1 Robot with Keyboard Control")
  print("=" * 50)
  print("📋 Controls:")
  print("   W/S: Forward/Backward")
  print("   A/D: Strafe Left/Right") 
  print("   Q/E: Turn Left/Right")
  print("   Space: Stop")
  print("   ESC: Exit")
  print("=" * 50)
  
  try:
    viewer.launch(loader=load_callback)
  except KeyboardInterrupt:
    print("\n👋 Shutting down...")
  finally:
    # Clean up controller resources
    if 'policy_controller' in globals():
        policy_controller.cleanup()

#!/usr/bin/env python3
"""Deploy a G1 MJX policy in ONNX format to C MuJoCo - HID controller ready."""

import sys
import os
from etils import epath
import mujoco
import mujoco.viewer as viewer
import numpy as np
import onnxruntime as rt

# Add current directory to Python path for imports
sys.path.insert(0, str(epath.Path(__file__).parent))

# Import the G1 assets function
from mujoco_playground._src.locomotion.g1.base import get_assets

_HERE = epath.Path(__file__).parent
_G1_XML = _HERE / "mujoco_playground" / "_src" / "locomotion" / "g1" / "xmls" / "scene_mjx_feetonly_flat_terrain.xml"
_ONNX_DIR = _HERE / "mujoco_playground" / "experimental" / "sim2sim" / "onnx"


class HIDControllerInterface:
    """Interface for HID controller support with fallback to autonomous mode."""
    
    def __init__(self, vel_scale_x=1.0, vel_scale_y=1.0, vel_scale_rot=1.0):
        self.vel_scale_x = vel_scale_x
        self.vel_scale_y = vel_scale_y
        self.vel_scale_rot = vel_scale_rot
        
        # Try to initialize HID controller
        self.controller = None
        self.use_controller = False
        
        self._try_init_hid_controller()
    
    def _try_init_hid_controller(self):
        """Try to initialize HID controller."""
        try:
            # Set up library path for ARM64 hidapi
            os.environ['DYLD_LIBRARY_PATH'] = '/opt/homebrew/lib:' + os.environ.get('DYLD_LIBRARY_PATH', '')
            
            import hid
            
            # Enumerate devices to find PS5 controller
            devices = hid.enumerate()
            ps5_device = None
            
            # PS5 DualSense controller identifiers
            PS5_VENDOR_ID = 0x054C  # Sony
            PS5_PRODUCT_IDS = [0x0CE6, 0x0DF2]  # Different PS5 controller variants
            
            for device in devices:
                vendor_id = device['vendor_id']
                product_id = device['product_id']
                product = device['product_string'] or ''
                
                if vendor_id == PS5_VENDOR_ID and product_id in PS5_PRODUCT_IDS:
                    ps5_device = device
                    break
                elif 'dualsense' in product.lower() or 'ps5' in product.lower():
                    ps5_device = device
                    break
            
            if ps5_device:
                # Try to open the device
                self.controller = hid.device()
                self.controller.open_path(ps5_device['path'])
                self.controller.set_nonblocking(True)
                self.use_controller = True
                
                print("🎮 PS5 Controller connected via HID!")
                print("📋 Controls:")
                print("   Left Stick: Forward/Back + Strafe")
                print("   Right Stick: Turn Left/Right")
                return
            else:
                print("⚠️  No PS5 controller detected")
                
        except Exception as e:
            print(f"⚠️  HID controller initialization failed: {e}")
            print("💡 To enable PS5 controller:")
            print("   1. Connect PS5 controller via USB or Bluetooth")
            print("   2. Install hidapi: brew install hidapi")
            print("   3. Install Python hidapi: pip3 install hidapi")
        
        print("🤖 Using autonomous walking mode")
        self.use_controller = False
    
    def get_command(self):
        """Get movement command from controller or return autonomous command."""
        if self.use_controller and self.controller:
            try:
                # Read data from PS5 controller
                data = self.controller.read(64, timeout_ms=1)
                
                if data and len(data) >= 9:
                    # Parse PS5 DualSense input report
                    left_x = (data[1] - 128) / 128.0    # Strafe
                    left_y = -(data[2] - 128) / 128.0   # Forward/back (inverted)
                    right_x = (data[3] - 128) / 128.0   # Turn
                    
                    # Apply deadzone
                    def apply_deadzone(value, deadzone=0.1):
                        if abs(value) < deadzone:
                            return 0.0
                        return (value - deadzone * np.sign(value)) / (1.0 - deadzone)
                    
                    vx = apply_deadzone(left_y) * self.vel_scale_x
                    vy = apply_deadzone(left_x) * self.vel_scale_y
                    wz = apply_deadzone(right_x) * self.vel_scale_rot
                    
                    return np.array([vx, vy, wz], dtype=np.float32)
                    
            except Exception as e:
                # Don't spam errors for normal timeouts
                if "timeout" not in str(e).lower():
                    print(f"Controller read error: {e}")
        
        # Fallback to autonomous walking
        return np.array([0.3, 0.0, 0.0], dtype=np.float32)
    
    def cleanup(self):
        """Clean up controller resources."""
        if self.controller:
            try:
                self.controller.close()
            except:
                pass


class OnnxController:
  """ONNX controller for the G1 robot with HID controller support."""

  def __init__(
      self,
      policy_path: str,
      default_angles: np.ndarray,
      ctrl_dt: float,
      n_substeps: int,
      action_scale: float = 0.5,
      vel_scale_x: float = 1.0,
      vel_scale_y: float = 1.0,
      vel_scale_rot: float = 1.0,
  ):
    self._output_names = ["continuous_actions"]
    self._policy = rt.InferenceSession(
        policy_path, providers=["CPUExecutionProvider"]
    )

    self._action_scale = action_scale
    self._default_angles = default_angles
    self._last_action = np.zeros_like(default_angles, dtype=np.float32)

    self._counter = 0
    self._n_substeps = n_substeps

    self._phase = np.array([0.0, np.pi])
    self._gait_freq = 1.5
    self._phase_dt = 2 * np.pi * self._gait_freq * ctrl_dt

    # Initialize HID controller interface
    self._hid_controller = HIDControllerInterface(
        vel_scale_x=vel_scale_x,
        vel_scale_y=vel_scale_y,
        vel_scale_rot=vel_scale_rot
    )

  def get_obs(self, model, data) -> np.ndarray:
    linvel = data.sensor("local_linvel_pelvis").data
    gyro = data.sensor("gyro_pelvis").data
    imu_xmat = data.site_xmat[model.site("imu_in_pelvis").id].reshape(3, 3)
    gravity = imu_xmat.T @ np.array([0, 0, -1])
    joint_angles = data.qpos[7:] - self._default_angles
    joint_velocities = data.qvel[6:]
    phase = np.concatenate([np.cos(self._phase), np.sin(self._phase)])
    
    # Get command from HID controller
    command = self._hid_controller.get_command()
    
    obs = np.hstack([
        linvel,
        gyro,
        gravity,
        command,
        joint_angles,
        joint_velocities,
        self._last_action,
        phase,
    ])
    return obs.astype(np.float32)

  def get_control(self, model: mujoco.MjModel, data: mujoco.MjData) -> None:
    self._counter += 1
    if self._counter % self._n_substeps == 0:
      obs = self.get_obs(model, data)
      onnx_input = {"obs": obs.reshape(1, -1)}
      onnx_pred = self._policy.run(self._output_names, onnx_input)[0][0]
      self._last_action = onnx_pred.copy()
      data.ctrl[:] = onnx_pred * self._action_scale + self._default_angles
      phase_tp1 = self._phase + self._phase_dt
      self._phase = np.fmod(phase_tp1 + np.pi, 2 * np.pi) - np.pi

  def cleanup(self):
    """Clean up controller resources."""
    self._hid_controller.cleanup()


def load_callback(model=None, data=None):
  mujoco.set_mjcb_control(None)

  model = mujoco.MjModel.from_xml_path(
      _G1_XML.as_posix(),
      assets=get_assets()
  )
  data = mujoco.MjData(model)

  mujoco.mj_resetDataKeyframe(model, data, 0)

  ctrl_dt = 0.02
  sim_dt = 0.005
  n_substeps = int(round(ctrl_dt / sim_dt))
  model.opt.timestep = sim_dt

  global policy_controller
  policy_controller = OnnxController(
      policy_path=(_ONNX_DIR / "g1_policy.onnx").as_posix(),
      default_angles=np.array(model.keyframe("knees_bent").qpos[7:]),
      ctrl_dt=ctrl_dt,
      n_substeps=n_substeps,
      action_scale=0.5,
      vel_scale_x=1.5,
      vel_scale_y=0.8,
      vel_scale_rot=1.5,
  )

  mujoco.set_mjcb_control(policy_controller.get_control)

  return model, data


if __name__ == "__main__":
  print("🤖 G1 Robot with HID Controller Support")
  print("=" * 50)
  print("📋 Setup Instructions:")
  print("1. Connect your PS5 DualSense controller via USB or Bluetooth")
  print("2. On macOS: Go to System Preferences > Bluetooth and pair the controller")
  print("3. Install hidapi: brew install hidapi")
  print("4. Install Python hidapi: pip3 install hidapi")
  print("5. If no controller is detected, the robot will walk autonomously")
  print("=" * 50)
  
  try:
    viewer.launch(loader=load_callback)
  except KeyboardInterrupt:
    print("\n👋 Shutting down...")
  finally:
    # Clean up controller resources
    if 'policy_controller' in globals():
        policy_controller.cleanup()

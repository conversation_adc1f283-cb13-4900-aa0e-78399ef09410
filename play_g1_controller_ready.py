#!/usr/bin/env python3
"""Deploy a G1 MJX policy in ONNX format to C MuJoCo - PS5 controller ready."""

import sys
from etils import epath
import mujoco
import mujoco.viewer as viewer
import numpy as np
import onnxruntime as rt

# Add current directory to Python path for imports
sys.path.insert(0, str(epath.Path(__file__).parent))

# Import the G1 assets function
from mujoco_playground._src.locomotion.g1.base import get_assets

_HERE = epath.Path(__file__).parent
_G1_XML = _HERE / "mujoco_playground" / "_src" / "locomotion" / "g1" / "xmls" / "scene_mjx_feetonly_flat_terrain.xml"
_ONNX_DIR = _HERE / "mujoco_playground" / "experimental" / "sim2sim" / "onnx"


class OnnxController:
  """ONNX controller for the G1 robot with PS5 controller support."""

  def __init__(
      self,
      policy_path: str,
      default_angles: np.ndarray,
      ctrl_dt: float,
      n_substeps: int,
      action_scale: float = 0.5,
      vel_scale_x: float = 1.0,
      vel_scale_y: float = 1.0,
      vel_scale_rot: float = 1.0,
  ):
    self._output_names = ["continuous_actions"]
    self._policy = rt.InferenceSession(
        policy_path, providers=["CPUExecutionProvider"]
    )

    self._action_scale = action_scale
    self._default_angles = default_angles
    self._last_action = np.zeros_like(default_angles, dtype=np.float32)

    self._counter = 0
    self._n_substeps = n_substeps

    self._phase = np.array([0.0, np.pi])
    self._gait_freq = 1.5
    self._phase_dt = 2 * np.pi * self._gait_freq * ctrl_dt

    # Store controller parameters
    self._vel_scale_x = vel_scale_x
    self._vel_scale_y = vel_scale_y
    self._vel_scale_rot = vel_scale_rot
    
    # Controller state
    self._ps5_controller = None
    self._use_controller = False
    self._controller_init_attempted = False
    
    print("🎮 G1 Robot ready for PS5 controller!")
    print("📋 To enable PS5 control:")
    print("   1. Connect PS5 DualSense controller via USB or Bluetooth")
    print("   2. Controller will be detected automatically")
    print("   3. Left Stick: Forward/Back + Strafe")
    print("   4. Right Stick: Turn Left/Right")
    print("🤖 Currently using autonomous walking mode")

  def _try_init_controller(self):
    """Try to initialize PS5 controller if not already attempted."""
    if not self._controller_init_attempted:
        self._controller_init_attempted = True
        try:
            # Import pygame here to avoid crashes during initialization
            import pygame
            pygame.init()
            pygame.joystick.init()
            
            joystick_count = pygame.joystick.get_count()
            if joystick_count > 0:
                joystick = pygame.joystick.Joystick(0)
                joystick.init()
                print(f"🎮 Controller detected: {joystick.get_name()}")
                
                # Create a simple controller interface
                self._joystick = joystick
                self._use_controller = True
                print("✅ PS5 Controller activated!")
                print("📋 Controls active:")
                print("   Left Stick: Forward/Back + Strafe")
                print("   Right Stick: Turn Left/Right")
            else:
                print("⚠️  No controller detected")
                
        except Exception as e:
            print(f"⚠️  Controller initialization failed: {e}")
            self._use_controller = False

  def _get_controller_command(self):
    """Get command from controller if available."""
    if not self._use_controller or not hasattr(self, '_joystick'):
        return np.array([0.3, 0.0, 0.0])  # Default forward walk
    
    try:
        import pygame
        pygame.event.pump()  # Update joystick state
        
        # Left stick: movement (X=strafe, Y=forward/back)
        left_x = self._joystick.get_axis(0)  # Left stick X (strafe)
        left_y = -self._joystick.get_axis(1)  # Left stick Y (forward/back, inverted)
        
        # Right stick: rotation
        right_x = self._joystick.get_axis(2)  # Right stick X (rotation)
        
        # Apply deadzone
        deadzone = 0.1
        def apply_deadzone(value):
            if abs(value) < deadzone:
                return 0.0
            return (value - deadzone * np.sign(value)) / (1.0 - deadzone)
        
        vx = apply_deadzone(left_y) * self._vel_scale_x
        vy = apply_deadzone(left_x) * self._vel_scale_y
        wz = apply_deadzone(right_x) * self._vel_scale_rot
        
        return np.array([vx, vy, wz])
        
    except Exception as e:
        print(f"Controller read error: {e}")
        return np.array([0.3, 0.0, 0.0])  # Fallback to autonomous

  def get_obs(self, model, data) -> np.ndarray:
    # Try to initialize controller on first call
    self._try_init_controller()
    
    linvel = data.sensor("local_linvel").data
    gyro = data.sensor("gyro").data
    imu_xmat = data.site_xmat[model.site("imu").id].reshape(3, 3)
    gravity = imu_xmat.T @ np.array([0, 0, -1])
    joint_angles = data.qpos[7:] - self._default_angles
    joint_velocities = data.qvel[6:]
    phase = np.concatenate([np.cos(self._phase), np.sin(self._phase)])
    
    # Get command from controller or use autonomous mode
    command = self._get_controller_command()
    
    obs = np.hstack([
        linvel,
        gyro,
        gravity,
        command,
        joint_angles,
        joint_velocities,
        self._last_action,
        phase,
    ])
    return obs.astype(np.float32)

  def get_control(self, model: mujoco.MjModel, data: mujoco.MjData) -> None:
    self._counter += 1
    if self._counter % self._n_substeps == 0:
      obs = self.get_obs(model, data)
      onnx_input = {"obs": obs.reshape(1, -1)}
      onnx_pred = self._policy.run(self._output_names, onnx_input)[0][0]
      self._last_action = onnx_pred.copy()
      data.ctrl[:] = onnx_pred * self._action_scale + self._default_angles
      phase_tp1 = self._phase + self._phase_dt
      self._phase = np.fmod(phase_tp1 + np.pi, 2 * np.pi) - np.pi

  def cleanup(self):
    """Clean up controller resources."""
    if hasattr(self, '_joystick'):
        try:
            import pygame
            pygame.quit()
        except:
            pass


def load_callback(model=None, data=None):
  mujoco.set_mjcb_control(None)

  model = mujoco.MjModel.from_xml_path(
      _G1_XML.as_posix(),
      assets=get_assets()
  )
  data = mujoco.MjData(model)

  mujoco.mj_resetDataKeyframe(model, data, 0)

  ctrl_dt = 0.02
  sim_dt = 0.005
  n_substeps = int(round(ctrl_dt / sim_dt))
  model.opt.timestep = sim_dt

  global policy_controller
  policy_controller = OnnxController(
      policy_path=(_ONNX_DIR / "g1_policy.onnx").as_posix(),
      default_angles=np.array(model.keyframe("knees_bent").qpos[7:]),
      ctrl_dt=ctrl_dt,
      n_substeps=n_substeps,
      action_scale=0.5,
      vel_scale_x=1.5,
      vel_scale_y=0.8,
      vel_scale_rot=1.5,
  )

  mujoco.set_mjcb_control(policy_controller.get_control)

  return model, data


if __name__ == "__main__":
  print("🤖 G1 Robot with PS5 Controller Support")
  print("=" * 50)
  print("📋 Setup Instructions:")
  print("1. Connect your PS5 DualSense controller via USB or Bluetooth")
  print("2. On macOS: Go to System Preferences > Bluetooth and pair the controller")
  print("3. The controller should appear as 'DualSense Wireless Controller'")
  print("4. If no controller is detected, the robot will walk autonomously")
  print("=" * 50)
  
  try:
    viewer.launch(loader=load_callback)
  except KeyboardInterrupt:
    print("\n👋 Shutting down...")
  finally:
    # Clean up controller resources
    if 'policy_controller' in globals():
        policy_controller.cleanup()

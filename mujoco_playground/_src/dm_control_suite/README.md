# DeepMind Control Suite

A re-implementation of the [DeepMind Control Suite](https://arxiv.org/abs/1801.00690) using [MJX](https://mujoco.readthedocs.io/en/latest/mjx.html).

## Contents

* ✅: Implemented
* ❌: Not implemented

| Env                       | Implemented   |
| ------------------------- | ------------- |
| acrobot-swingup           | ✅            |
| acrobot-swingup_sparse    | ✅            |
| ball_in_cup-catch         | ✅            |
| cartpole-balance          | ✅            |
| cartpole-balance_sparse   | ✅            |
| cartpole-swingup          | ✅            |
| cartpole-swingup_sparse   | ✅            |
| cheetah-run               | ✅            |
| finger-spin               | ✅            |
| finger_turn_easy          | ✅            |
| finger_turn_hard          | ✅            |
| fish-upright              | ✅            |
| fish-swim                 | ✅            |
| hopper-stand              | ✅            |
| hopper-hop                | ✅            |
| humanoid-stand            | ✅            |
| humanoid-walk             | ✅            |
| humanoid-run              | ✅            |
| pendulum-swingup          | ✅            |
| point_mass-easy           | ✅            |
| reacher-easy              | ✅            |
| reacher-hard              | ✅            |
| swimmer-swimmer6          | ✅            |
| swimmer-swimmer15         | ✅            |
| walker-stand              | ✅            |
| walker-walk               | ✅            |
| walker-run                | ✅            |
| manipulator-bring_ball    | ❌            |
| manipulator-bring_peg     | ❌            |
| manipulator-insert_ball   | ❌            |
| manipulator-insert_peg    | ❌            |
| dog-stand                 | ❌            |
| dog-walk                  | ❌            |
| dog-trot                  | ❌            |
| dog-run                   | ❌            |
| dog-fetch                 | ❌            |

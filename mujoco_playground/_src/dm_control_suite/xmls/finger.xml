<mujoco model="finger">
  <include file="./common/visual.xml"/>
  <include file="./common/skybox.xml"/>
  <include file="./common/materials.xml"/>

  <option timestep="0.005" integrator="implicitfast" iterations="2" ls_iterations="8">
    <flag gravity="disable" eulerdamp="disable"/>
  </option>
  <!-- <option timestep="0.01" iterations="200" ls_iterations="4">
    <flag gravity="disable" eulerdamp="disable"/>
  </option> -->

  <custom>
    <numeric data="4" name="max_contact_points"/>
    <numeric data="2" name="max_geom_pairs"/>
  </custom>

  <default>
    <geom solimp="0 0.9 0.01" solref=".02 1"/>
    <joint type="hinge" axis="0 -1 0"/>
    <motor ctrllimited="true" ctrlrange="-1 1"/>
    <default class="finger">
      <joint damping="2.5" limited="true"/>
      <site type="ellipsoid" size=".025 .03 .025" material="site" group="3"/>
    </default>
  </default>

  <worldbody>
    <light name="light" directional="true" diffuse=".6 .6 .6" pos="0 0 2" specular=".3 .3 .3"/>
    <geom name="ground" type="plane" pos="0 0 0" size=".6 .2 10" material="grid"/>
    <camera name="cam0" pos="0 -1 .8" xyaxes="1 0 0 0 1 2"/>
    <camera name="cam1" pos="0 -1 .4" xyaxes="1 0 0 0 0 1" />

    <body name="proximal" pos="-.2 0 .4" childclass="finger">
      <geom name="proximal_decoration" type="cylinder" fromto="0 -.033 0 0 .033 0" size=".034" material="decoration"
        contype="0" conaffinity="0"/>
      <joint name="proximal" range="-110 110" ref="-90"/>
      <geom name="proximal" type="capsule" material="self" size=".03" fromto="0 0 0 0 0 -.17"/>
      <body name="distal" pos="0 0 -.18" childclass="finger">
        <joint name="distal" range="-110 110"/>
        <geom name="distal" type="capsule" size=".028" material="self" fromto="0 0 0 0 0 -.16" contype="0" conaffinity="0"/>
        <geom name="fingertip" type="capsule" size=".03" material="effector" fromto="0 0 -.13 0 0 -.161"/>
        <site name="touchtop" pos=".01 0 -.17"/>
        <site name="touchbottom" pos="-.01 0 -.17"/>
      </body>
    </body>

    <body name="spinner" pos=".2 0 .4">
      <joint name="hinge" frictionloss=".1" damping=".5"/>
      <geom name="cap1" type="capsule" size=".04 .09" material="self" pos=".02 0 0"/>
      <geom name="cap2" type="capsule" size=".04 .09" material="self" pos="-.02 0 0"/>
      <site name="tip" type="sphere"  size=".02" pos="0 0 .13" material="target"/>
      <geom name="spinner_decoration" type="cylinder" fromto="0 -.045 0 0 .045 0" size=".02" material="decoration"
        contype="0" conaffinity="0"/>
    </body>

    <body name="target" mocap="true" pos="0 0 .4">
      <site name="target" type="sphere" size=".03" material="target"/>
    </body>
  </worldbody>

  <actuator>
    <motor name="proximal" joint="proximal" gear="30"/>
    <motor name="distal" joint="distal" gear="15"/>
  </actuator>

  <!-- All finger observations are functions of sensors. This is useful for finite-differencing. -->
  <sensor>
    <jointpos name="proximal" joint="proximal"/>
    <jointpos name="distal" joint="distal"/>
    <jointvel name="proximal_velocity" joint="proximal"/>
    <jointvel name="distal_velocity" joint="distal"/>
    <jointvel name="hinge_velocity" joint="hinge"/>
    <framepos name="tip" objtype="site" objname="tip"/>
    <framepos name="target" objtype="site" objname="target"/>
    <framepos name="spinner" objtype="xbody" objname="spinner"/>
    <touch name="touchtop" site="touchtop"/>
    <touch name="touchbottom" site="touchbottom"/>
    <framepos name="touchtop_pos" objtype="site" objname="touchtop"/>
    <framepos name="touchbottom_pos" objtype="site" objname="touchbottom"/>
  </sensor>

</mujoco>

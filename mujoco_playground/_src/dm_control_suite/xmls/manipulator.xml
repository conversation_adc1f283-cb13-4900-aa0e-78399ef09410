<mujoco model="planar manipulator">

  <include file="./common/visual.xml"/>
  <include file="./common/skybox.xml"/>
  <include file="./common/materials.xml"/>
  <asset>
    <texture name="background" builtin="flat" type="2d" mark="random" markrgb="1 1 1" width="800" height="800" rgb1=".2 .3 .4"/>
    <material name="background" texture="background" texrepeat="1 1" texuniform="true"/>
  </asset>

  <visual>
    <map shadowclip=".5"/>
    <quality shadowsize="2048"/>
  </visual>

  <option timestep="0.005" cone="elliptic" iterations="4" ls_iterations="10"/>

  <custom>
    <numeric data="8" name="max_contact_points"/>
    <numeric data="8" name="max_geom_pairs"/>
  </custom>

  <default>
    <geom friction=".7" solimp="0.9 0.97 0.001" solref=".005 1"/>
    <joint solimplimit="0 0.99 0.01" solreflimit=".005 1"/>
    <general ctrllimited="true"/>
    <tendon width="0.01"/>
    <site size=".003 .003 .003" material="site" group="3"/>

    <default class="arm">
      <geom type="capsule" material="self" density="500"/>
      <joint type="hinge" pos="0 0 0" axis="0 -1 0" limited="true"/>
      <default class="hand">
        <joint damping=".5" range="-10 60"/>
        <geom size=".008"/>
        <site  type="box" size=".018 .005 .005" pos=".022 0 -.002" euler="0 15 0" group="4"/>
        <default class="fingertip">
          <geom type="sphere" size=".008" material="effector"/>
          <joint damping=".01" stiffness=".01" range="-40 20"/>
          <site  size=".012 .005 .008" pos=".003 0 .003" group="4" euler="0 0 0"/>
        </default>
      </default>
    </default>

    <default class="object">
      <geom material="self"/>
    </default>

    <default class="task">
      <site rgba="0 0 0 0"/>
    </default>

    <default class="obstacle">
      <geom material="decoration" friction="0"/>
    </default>

    <default class="ghost">
      <geom material="target" contype="0" conaffinity="0"/>
    </default>
  </default>

  <worldbody>
    <!-- Arena -->
    <light name="light" directional="true" diffuse=".6 .6 .6" pos="0 0 1" specular=".3 .3 .3"/>
    <geom name="floor" type="plane" pos="0 0 0" size=".4 .2 10" material="grid"/>
    <geom name="wall1" type="plane" pos="-.682843 0 .282843" size=".4 .2 10" material="grid" zaxis="1 0 1"/>
    <geom name="wall2" type="plane" pos=".682843 0 .282843" size=".4 .2 10" material="grid" zaxis="-1 0 1"/>
    <geom name="background" type="plane" pos="0 .2 .5" size="1 .5 10" material="background" zaxis="0 -1 0"/>
    <camera name="fixed" pos="0 -16 .4" xyaxes="1 0 0 0 0 1" fovy="4"/>

    <!-- Arm -->
    <geom name="arm_root" type="cylinder" fromto="0 -.022 .4 0 .022 .4" size=".024"
          material="decoration" contype="0" conaffinity="0"/>
    <body name="upper_arm" pos="0 0 .4" childclass="arm">
      <joint name="arm_root" damping="2" limited="false"/>
      <geom  name="upper_arm"  size=".02" fromto="0 0 0 0 0 .18"/>
      <body  name="middle_arm" pos="0 0 .18" childclass="arm">
        <joint name="arm_shoulder" damping="1.5" range="-160 160"/>
        <geom  name="middle_arm"  size=".017" fromto="0 0 0 0 0 .15"/>
        <body  name="lower_arm" pos="0 0 .15">
          <joint name="arm_elbow" damping="1" range="-160 160"/>
          <geom  name="lower_arm" size=".014" fromto="0 0 0 0 0 .12"/>
          <body  name="hand" pos="0 0 .12">
            <joint name="arm_wrist" damping=".5" range="-140 140" />
            <geom  name="hand" size=".011" fromto="0 0 0 0 0 .03"/>
            <geom  name="palm1"  fromto="0 0 .03  .03 0 .045" class="hand"/>
            <geom  name="palm2"  fromto="0 0 .03 -.03 0 .045" class="hand"/>
            <site  name="grasp" pos="0 0 .065"/>
            <body  name="pinch site" pos="0 0 .090">
              <site  name="pinch"/>
              <inertial pos="0 0 0" mass="1e-6" diaginertia="1e-12 1e-12 1e-12"/>
              <camera name="hand" pos="0 -.3 0" xyaxes="1 0 0 0 0 1" mode="track"/>
            </body>
            <site  name="palm_touch" type="box" group="4" size=".025 .005 .008" pos="0 0 .043"/>

            <body name="thumb" pos=".03 0 .045" euler="0 -90 0" childclass="hand">
              <joint name="thumb"/>
              <geom  name="thumb1"  fromto="0 0 0 .02 0 -.01" size=".007"/>
              <geom  name="thumb2"  fromto=".02 0 -.01 .04 0 -.01" size=".007"/>
              <site  name="thumb_touch" group="4"/>
              <body  name="thumbtip" pos=".05 0 -.01" childclass="fingertip">
                <joint name="thumbtip"/>
                <geom  name="thumbtip1" pos="-.003 0 0" />
                <geom  name="thumbtip2" pos=".003 0 0" />
                <site  name="thumbtip_touch" group="4"/>
              </body>
            </body>

            <body name="finger" pos="-.03 0 .045" euler="0 90 180" childclass="hand">
              <joint name="finger"/>
              <geom  name="finger1"  fromto="0 0 0 .02 0 -.01" size=".007" />
              <geom  name="finger2"  fromto=".02 0 -.01 .04 0 -.01" size=".007"/>
              <site  name="finger_touch"/>
              <body  name="fingertip" pos=".05 0 -.01" childclass="fingertip">
                <joint name="fingertip"/>
                <geom  name="fingertip1" pos="-.003 0 0" />
                <geom  name="fingertip2" pos=".003 0 0" />
                <site  name="fingertip_touch"/>
              </body>
            </body>
          </body>
        </body>
      </body>
    </body>

    <!-- props -->
    <body name="ball" pos=".4 0 .4" childclass="object">
      <joint name="ball_x" type="slide" axis="1 0 0" ref=".4"/>
      <joint name="ball_z" type="slide" axis="0 0 1" ref=".4"/>
      <joint name="ball_y" type="hinge" axis="0 1 0"/>
      <geom  name="ball" type="sphere" size=".022" />
      <site  name="ball" type="sphere"/>
    </body>

    <body name="peg" pos="-.4 0 .4" childclass="object">
      <joint name="peg_x" type="slide" axis="1 0 0" ref="-.4"/>
      <joint name="peg_z" type="slide" axis="0 0 1" ref=".4"/>
      <joint name="peg_y" type="hinge" axis="0 1 0"/>
      <geom name="blade" type="capsule" size=".005" fromto="0 0 -.013 0 0 -.113"/>
      <geom name="guard" type="capsule" size=".005" fromto="-.017 0 -.043 .017 0 -.043"/>
      <body name="pommel" pos="0 0 -.013">
        <geom name="pommel" type="sphere" size=".009"/>
      </body>
      <site name="peg" type="box" pos="0 0 -.063"/>
      <site name="peg_pinch" type="box" pos="0 0 -.025"/>
      <site name="peg_grasp" type="box" pos="0 0 0"/>
      <site name="peg_tip"   type="box" pos="0 0 -.113"/>
    </body>

    <!-- receptacles -->
    <body name="slot" pos="-.405 0 .2" euler="0 20 0" childclass="obstacle">
      <geom name="slot_0" type="box" pos="-.0252 0 -.083" size=".0198 .01 .035"/>
      <geom name="slot_1" type="box" pos=" .0252 0 -.083" size=".0198 .01 .035"/>
      <geom name="slot_2" type="box" pos="  0   0 -.138" size=".045 .01 .02"/>
      <site name="slot" type="box" pos="0 0 0"/>
      <site name="slot_end" type="box" pos="0 0 -.05"/>
    </body>

    <body name="cup" pos=".3 0 .4" euler="0 -15 0" childclass="obstacle">
      <geom name="cup_0" type="capsule" size=".008" fromto="-.03 0 .06 -.03 0 -.015" />
      <geom name="cup_1" type="capsule" size=".008" fromto="-.03 0 -.015 0 0 -.04" />
      <geom name="cup_2" type="capsule" size=".008" fromto="0 0 -.04 .03 0 -.015" />
      <geom name="cup_3" type="capsule" size=".008" fromto=".03 0 -.015 .03 0 .06" />
      <site name="cup" size=".005"/>
    </body>

    <!-- targets -->
    <body name="target_ball" pos=".4 .001 .4" childclass="ghost">
      <geom  name="target_ball" type="sphere" size=".02" />
      <site  name="target_ball" type="sphere"/>
    </body>

    <body name="target_peg" pos="-.2 .001 .4" childclass="ghost">
      <geom name="target_blade" type="capsule" size=".005" fromto="0 0 -.013 0 0 -.113"/>
      <geom name="target_guard" type="capsule" size=".005" fromto="-.017 0 -.043 .017 0 -.043"/>
      <geom name="target_pommel" type="sphere" size=".009" pos="0 0 -.013"/>
      <site name="target_peg" type="box" pos="0 0 -.063"/>
      <site name="target_peg_pinch" type="box" pos="0 0 -.025"/>
      <site name="target_peg_grasp" type="box" pos="0 0 0"/>
      <site name="target_peg_tip"   type="box" pos="0 0 -.113"/>
    </body>

  </worldbody>

  <tendon>
    <fixed name="grasp">
      <joint joint="thumb"  coef=".5"/>
      <joint joint="finger" coef=".5"/>
    </fixed>
    <fixed name="coupling">
      <joint joint="thumb"  coef="-.5"/>
      <joint joint="finger" coef=".5"/>
    </fixed>
  </tendon>

  <equality>
    <tendon name="coupling" tendon1="coupling" solimp="0.95 0.99 0.001" solref=".005 .5"/>
  </equality>

  <sensor>
    <touch name="palm_touch" site="palm_touch"/>
    <touch name="finger_touch" site="finger_touch"/>
    <touch name="thumb_touch" site="thumb_touch"/>
    <touch name="fingertip_touch" site="fingertip_touch"/>
    <touch name="thumbtip_touch" site="thumbtip_touch"/>
  </sensor>

  <actuator>
    <motor name="root"     joint="arm_root"     ctrlrange="-1 1"  gear="12"/>
    <motor name="shoulder" joint="arm_shoulder" ctrlrange="-1 1"  gear="8"/>
    <motor name="elbow"    joint="arm_elbow"    ctrlrange="-1 1"  gear="4"/>
    <motor name="wrist"    joint="arm_wrist"    ctrlrange="-1 1"  gear="2"/>
    <motor name="grasp"    tendon="grasp"       ctrlrange="-1 1"  gear="2"/>
  </actuator>

</mujoco>

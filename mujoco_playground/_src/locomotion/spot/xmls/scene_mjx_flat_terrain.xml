<mujoco model="spot flat terrain scene">
  <include file="spot_mjx.xml"/>

  <statistic center="0 0 0.1" extent="0.8" meansize="0.04"/>

  <visual>
    <headlight diffuse=".8 .8 .8" ambient=".2 .2 .2" specular="1 1 1"/>
    <rgba force="1 0 0 1"/>
    <global azimuth="120" elevation="-20"/>
    <map force="0.01"/>
    <scale forcewidth="0.3" contactwidth="0.5" contactheight="0.2"/>
    <quality shadowsize="8192"/>
  </visual>

  <asset>
    <texture type="skybox" builtin="gradient" rgb1="1 1 1" rgb2="1 1 1" width="800" height="800"/>
    <texture type="2d" name="groundplane" builtin="checker" mark="edge" rgb1="1 1 1" rgb2="1 1 1" markrgb="0 0 0"
      width="300" height="300"/>
    <material name="groundplane" texture="groundplane" texuniform="true" texrepeat="5 5" reflectance="0"/>
  </asset>

  <worldbody>
    <geom name="floor" size="0 0 0.01" type="plane" material="groundplane" contype="1" conaffinity="0" priority="1"
      friction="0.6" condim="3"/>
  </worldbody>

  <keyframe>
    <key name="home" qpos="
    0 0 0.46
    1 0 0 0
    0 1.04 -1.8
    0 1.04 -1.8
    0 1.04 -1.8
    0 1.04 -1.8"
      ctrl="
    0 1.04 -1.8
    0 1.04 -1.8
    0 1.04 -1.8
    0 1.04 -1.8"/>
  </keyframe>
</mujoco>

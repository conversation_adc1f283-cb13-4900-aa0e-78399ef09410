<mujoco model="h1">
  <compiler angle="radian" meshdir="../mujoco_menagerie/unitree_h1/assets" autolimits="true"/>

  <option iterations="1" ls_iterations="5" timestep="0.004">
    <flag eulerdamp="disable"/>
  </option>

  <default>
    <default class="h1">
      <geom contype="0" conaffinity="0"/>
      <position inheritrange="1" dampratio="1"/>
      <joint damping="1" armature="0.1"/>
      <default class="visual">
        <geom type="mesh" group="2" material="black"/>
      </default>
      <default class="collision">
        <geom group="3" mass="0" density="0"/>
        <default class="foot">
          <geom type="capsule" size=".014" contype="0" conaffinity="1" friction="1.0"/>
          <default class="foot1">
            <geom fromto="-.035 0 -0.056 .02 0 -0.045"/>
          </default>
          <default class="foot2">
            <geom fromto=".02 0 -0.045 .115 0 -0.056"/>
          </default>
          <default class="foot3">
            <geom fromto=".14 -.03 -0.056 .14 .03 -0.056"/>
          </default>
        </default>
      </default>
      <site size="0.01" rgba="1 0 0 1" group="5"/>
    </default>
  </default>

  <asset>
    <material name="black" rgba="0.2 0.2 0.2 1"/>
    <material name="white" rgba="1 1 1 1"/>

    <mesh file="pelvis.stl"/>
    <mesh file="left_hip_yaw_link.stl"/>
    <mesh file="left_hip_roll_link.stl"/>
    <mesh file="left_hip_pitch_link.stl"/>
    <mesh file="left_knee_link.stl"/>
    <mesh file="left_ankle_link.stl"/>
    <mesh file="right_hip_yaw_link.stl"/>
    <mesh file="right_hip_roll_link.stl"/>
    <mesh file="right_hip_pitch_link.stl"/>
    <mesh file="right_knee_link.stl"/>
    <mesh file="right_ankle_link.stl"/>
    <mesh file="torso_link.stl"/>
    <mesh file="left_shoulder_pitch_link.stl"/>
    <mesh file="left_shoulder_roll_link.stl"/>
    <mesh file="left_shoulder_yaw_link.stl"/>
    <mesh file="left_elbow_link.stl"/>
    <mesh file="right_shoulder_pitch_link.stl"/>
    <mesh file="right_shoulder_roll_link.stl"/>
    <mesh file="right_shoulder_yaw_link.stl"/>
    <mesh file="right_elbow_link.stl"/>
    <mesh file="logo_link.stl"/>
  </asset>

  <worldbody>
    <light name="spotlight" mode="targetbodycom" target="torso_link" pos="3 0 4"/>
    <body name="pelvis" pos="0 0 1.06" childclass="h1">
      <inertial pos="-0.0002 4e-05 -0.04522" quat="0.498303 0.499454 -0.500496 0.501741" mass="5.39"
        diaginertia="0.0490211 0.0445821 0.00824619"/>
      <freejoint/>
      <camera name="track" pos="1.678 -2.065 .499" xyaxes="0.781 0.624 0.000 -0.127 0.159 0.979" mode="trackcom"/>
      <camera name="side" pos="0 -2.2 1" xyaxes="1 0 0 0 1 2" mode="trackcom"/>
      <camera name="back" pos="-2.2 0 1" xyaxes="0 -1 0 1 0 2" mode="trackcom"/>
      <camera name="front" pos="2.2 0 1" xyaxes="0 1 0 -1 0 2" mode="trackcom"/>
      <geom class="visual" mesh="pelvis"/>
      <body name="left_hip_yaw_link" pos="0 0.0875 -0.1742">
        <inertial pos="-0.04923 0.0001 0.0072" quat="0.69699 0.219193 0.233287 0.641667" mass="2.244"
          diaginertia="0.00304494 0.00296885 0.00189201"/>
        <joint name="left_hip_yaw" axis="0 0 1" range="-0.43 0.43"/>
        <geom class="visual" mesh="left_hip_yaw_link"/>
        <geom size="0.06 0.035" pos="-0.067 0 0" quat="0.707123 0 0.70709 0" type="cylinder" class="collision"/>
        <body name="left_hip_roll_link" pos="0.039468 0 0">
          <inertial pos="-0.0058 -0.00319 -9e-05" quat="0.0438242 0.70721 -0.0729075 0.701867" mass="2.232"
            diaginertia="0.00243264 0.00225325 0.00205492"/>
          <joint name="left_hip_roll" axis="1 0 0" range="-0.43 0.43"/>
          <geom class="visual" mesh="left_hip_roll_link"/>
          <geom class="collision" type="cylinder" size="0.05 0.03" quat="1 1 0 0" pos="0 -0.02 0"/>
          <body name="left_hip_pitch_link" pos="0 0.11536 0">
            <inertial pos="0.00746 -0.02346 -0.08193" quat="0.979828 0.0513522 -0.0169854 -0.192382" mass="4.152"
              diaginertia="0.0829503 0.0821457 0.00510909"/>
            <joint name="left_hip_pitch" axis="0 1 0" range="-1.57 1.57"/>
            <geom class="visual" mesh="left_hip_pitch_link"/>
            <geom class="collision" type="capsule" size="0.03" fromto="0.02 0 -0.4 -0.02 0 0.02"/>
            <geom class="collision" type="capsule" size="0.03" fromto="0.02 0 -0.4 0.02 0 0.02"/>
            <geom class="collision" type="cylinder" size="0.05 0.02" quat="1 1 0 0" pos="0 -0.07 0"/>
            <body name="left_knee_link" pos="0 0 -0.4">
              <inertial pos="-0.00136 -0.00512 -0.1384" quat="0.626132 -0.034227 -0.0416277 0.777852" mass="1.721"
                diaginertia="0.0125237 0.0123104 0.0019428"/>
              <joint name="left_knee" axis="0 1 0" range="-0.26 2.05"/>
              <geom class="visual" mesh="left_knee_link"/>
              <geom class="collision" type="capsule" size="0.025" fromto="0.02 0 -0.4 0.02 0 0"/>
              <geom class="collision" type="sphere" size="0.05" pos="0 0 -0.115"/>
              <body name="left_ankle_link" pos="0 0 -0.4">
                <inertial pos="0.06722 0.00015 -0.04497" quat="0.489101 0.503197 0.565782 0.432972" mass="0.446"
                  diaginertia="0.00220848 0.00218961 0.000214202"/>
                <joint name="left_ankle" axis="0 1 0" range="-0.87 0.52"/>
                <geom class="visual" mesh="left_ankle_link"/>
                <geom name="left_foot1" class="foot1"/>
                <geom name="left_foot2" class="foot2"/>
                <geom name="left_foot3" class="foot3"/>
                <site name="left_foot" size="0.015" pos="0.02 0 -0.045"/>
              </body>
            </body>
          </body>
        </body>
      </body>
      <body name="right_hip_yaw_link" pos="0 -0.0875 -0.1742">
        <inertial pos="-0.04923 -0.0001 0.0072" quat="0.641667 0.233287 0.219193 0.69699" mass="2.244"
          diaginertia="0.00304494 0.00296885 0.00189201"/>
        <joint name="right_hip_yaw" axis="0 0 1" range="-0.43 0.43"/>
        <geom class="visual" mesh="right_hip_yaw_link"/>
        <geom size="0.06 0.035" pos="-0.067 0 0" quat="0.707123 0 0.70709 0" type="cylinder" class="collision"/>
        <body name="right_hip_roll_link" pos="0.039468 0 0">
          <inertial pos="-0.0058 0.00319 -9e-05" quat="-0.0438242 0.70721 0.0729075 0.701867" mass="2.232"
            diaginertia="0.00243264 0.00225325 0.00205492"/>
          <joint name="right_hip_roll" axis="1 0 0" range="-0.43 0.43"/>
          <geom class="visual" mesh="right_hip_roll_link"/>
          <geom class="collision" type="cylinder" size="0.05 0.03" quat="1 1 0 0" pos="0 0.02 0"/>
          <body name="right_hip_pitch_link" pos="0 -0.11536 0">
            <inertial pos="0.00746 0.02346 -0.08193" quat="0.979828 -0.0513522 -0.0169854 0.192382" mass="4.152"
              diaginertia="0.0829503 0.0821457 0.00510909"/>
            <joint name="right_hip_pitch" axis="0 1 0" range="-1.57 1.57"/>
            <geom class="visual" mesh="right_hip_pitch_link"/>
            <geom class="collision" type="capsule" size="0.03" fromto="0.02 0 -0.4 -0.02 0 0.02"/>
            <geom class="collision" type="capsule" size="0.03" fromto="0.02 0 -0.4 0.02 0 0.02"/>
            <geom class="collision" type="cylinder" size="0.05 0.02" quat="1 1 0 0" pos="0 0.07 0"/>
            <body name="right_knee_link" pos="0 0 -0.4">
              <inertial pos="-0.00136 0.00512 -0.1384" quat="0.777852 -0.0416277 -0.034227 0.626132" mass="1.721"
                diaginertia="0.0125237 0.0123104 0.0019428"/>
              <joint name="right_knee" axis="0 1 0" range="-0.26 2.05"/>
              <geom class="visual" mesh="right_knee_link"/>
              <geom class="collision" type="capsule" size="0.025" fromto="0.02 0 -0.4 0.02 0 0"/>
              <geom class="collision" type="sphere" size="0.05" pos="0 0 -0.115"/>
              <body name="right_ankle_link" pos="0 0 -0.4">
                <inertial pos="0.06722 -0.00015 -0.04497" quat="0.432972 0.565782 0.503197 0.489101" mass="0.446"
                  diaginertia="0.00220848 0.00218961 0.000214202"/>
                <joint name="right_ankle" axis="0 1 0" range="-0.87 0.52"/>
                <geom class="visual" mesh="right_ankle_link"/>
                <geom name="right_foot1" class="foot1"/>
                <geom name="right_foot2" class="foot2"/>
                <geom name="right_foot3" class="foot3"/>
                <site name="right_foot" size="0.015" pos="0.02 0 -0.045"/>
              </body>
            </body>
          </body>
        </body>
      </body>
      <body name="torso_link">
        <site name="torso_frame"/>
        <inertial pos="0.000489 0.002797 0.20484" quat="0.999989 -0.00130808 -0.00282289 -0.00349105" mass="17.789"
          diaginertia="0.487315 0.409628 0.127837"/>
        <joint name="torso" axis="0 0 1" range="-2.35 2.35"/>
        <geom class="visual" mesh="torso_link"/>
        <geom class="visual" material="white" mesh="logo_link"/>
        <geom name="head" class="collision" type="capsule" size="0.06" fromto="0.05 0 0.68 0.05 0 0.6"/>
        <!-- <geom name="helmet" class="collision" type="sphere" size="0.073" pos="0.045 0 0.68"/> -->
        <geom name="torso" class="collision" type="box" size="0.07 0.1 0.22" pos="0 0 0.25"/>
        <geom name="hip" class="collision" type="capsule" size="0.05" fromto="0 -0.1 -0.05 0 0.1 -0.05"/>
        <site name="imu" pos="-0.04452 -0.01891 0.27756"/>
        <body name="left_shoulder_pitch_link" pos="0.0055 0.15535 0.42999" quat="0.976296 0.216438 0 0">
          <inertial pos="0.005045 0.053657 -0.015715" quat="0.814858 0.579236 -0.0201072 -0.00936488" mass="1.033"
            diaginertia="0.00129936 0.000987113 0.000858198"/>
          <joint name="left_shoulder_pitch" axis="0 1 0" range="-2.87 2.87"/>
          <geom class="visual" mesh="left_shoulder_pitch_link"/>
          <body name="left_shoulder_roll_link" pos="-0.0055 0.0565 -0.0165" quat="0.976296 -0.216438 0 0">
            <inertial pos="0.000679 0.00115 -0.094076" quat="0.732491 0.00917179 0.0766656 0.676384" mass="0.793"
              diaginertia="0.00170388 0.00158256 0.00100336"/>
            <joint name="left_shoulder_roll" axis="1 0 0" range="-0.34 3.11"/>
            <geom class="visual" mesh="left_shoulder_roll_link"/>
            <geom name="left_shoulder" class="collision" type="capsule" size="0.04" fromto="0 0.01 0.008 0 -0.07 -0.02"/>
            <body name="left_shoulder_yaw_link" pos="0 0 -0.1343">
              <inertial pos="0.01365 0.002767 -0.16266" quat="0.703042 -0.0331229 -0.0473362 0.708798" mass="0.839"
                diaginertia="0.00408038 0.00370367 0.000622687"/>
              <joint name="left_shoulder_yaw" axis="0 0 1" range="-1.3 4.45"/>
              <geom class="visual" mesh="left_shoulder_yaw_link"/>
              <geom class="collision" type="capsule" size="0.03" fromto="0 0 0.15 0 0 -0.2"/>
              <body name="left_elbow_link" pos="0.0185 0 -0.198">
                <inertial pos="0.15908 -0.000144 -0.015776" quat="0.0765232 0.720327 0.0853116 0.684102" mass="0.669"
                  diaginertia="0.00601829 0.00600579 0.000408305"/>
                <joint name="left_elbow" axis="0 1 0" range="-1.25 2.61"/>
                <geom class="visual" mesh="left_elbow_link"/>
                <geom class="collision" type="capsule" size="0.025" fromto="0 0 0 0.28 0 -0.015"/>
                <geom class="collision" type="sphere" size="0.033" pos="0.28 0 -0.015"/>
                <site name="left_wrist" pos="0.2605 0 -0.0185"/>
              </body>
            </body>
          </body>
        </body>
        <body name="right_shoulder_pitch_link" pos="0.0055 -0.15535 0.42999" quat="0.976296 -0.216438 0 0">
          <inertial pos="0.005045 -0.053657 -0.015715" quat="0.579236 0.814858 0.00936488 0.0201072" mass="1.033"
            diaginertia="0.00129936 0.000987113 0.000858198"/>
          <joint name="right_shoulder_pitch" axis="0 1 0" range="-2.87 2.87"/>
          <geom class="visual" mesh="right_shoulder_pitch_link"/>
          <body name="right_shoulder_roll_link" pos="-0.0055 -0.0565 -0.0165" quat="0.976296 0.216438 0 0">
            <inertial pos="0.000679 -0.00115 -0.094076" quat="0.676384 0.0766656 0.00917179 0.732491" mass="0.793"
              diaginertia="0.00170388 0.00158256 0.00100336"/>
            <joint name="right_shoulder_roll" axis="1 0 0" range="-3.11 0.34"/>
            <geom class="visual" mesh="right_shoulder_roll_link"/>
            <geom name="right_shoulder" class="collision" type="capsule" size="0.04" fromto="0 -0.01 0.008 0 0.07 -0.02"/>
            <body name="right_shoulder_yaw_link" pos="0 0 -0.1343">
              <inertial pos="0.01365 -0.002767 -0.16266" quat="0.708798 -0.0473362 -0.0331229 0.703042" mass="0.839"
                diaginertia="0.00408038 0.00370367 0.000622687"/>
              <joint name="right_shoulder_yaw" axis="0 0 1" range="-4.45 1.3"/>
              <geom class="visual" mesh="right_shoulder_yaw_link"/>
              <geom class="collision" type="capsule" size="0.03" fromto="0 0 0.15 0 0 -0.2"/>
              <body name="right_elbow_link" pos="0.0185 0 -0.198">
                <inertial pos="0.15908 0.000144 -0.015776" quat="-0.0765232 0.720327 -0.0853116 0.684102" mass="0.669"
                  diaginertia="0.00601829 0.00600579 0.000408305"/>
                <joint name="right_elbow" axis="0 1 0" range="-1.25 2.61"/>
                <geom class="visual" mesh="right_elbow_link"/>
                <geom class="collision" type="capsule" size="0.025" fromto="0 0 0 0.28 0 -0.015"/>
                <geom class="collision" type="sphere" size="0.033" pos="0.28 0 -0.015"/>
                <site name="right_wrist" pos="0.2605 0 -0.0185"/>
              </body>
            </body>
          </body>
        </body>
      </body>
    </body>
  </worldbody>

  <contact>
    <exclude body1="torso_link" body2="left_shoulder_roll_link"/>
    <exclude body1="torso_link" body2="right_shoulder_roll_link"/>
  </contact>

  <actuator>
    <position class="h1" name="left_hip_yaw" joint="left_hip_yaw" forcerange="-200 200" kp="800"/>
    <position class="h1" name="left_hip_roll" joint="left_hip_roll" forcerange="-200 200" kp="800"/>
    <position class="h1" name="left_hip_pitch" joint="left_hip_pitch" forcerange="-200 200" kp="800"/>
    <position class="h1" name="left_knee" joint="left_knee" forcerange="-300 300" kp="800"/>
    <position class="h1" name="left_ankle" joint="left_ankle" forcerange="-40 40" kp="800"/>

    <position class="h1" name="right_hip_yaw" joint="right_hip_yaw" forcerange="-200 200" kp="800"/>
    <position class="h1" name="right_hip_roll" joint="right_hip_roll" forcerange="-200 200" kp="800"/>
    <position class="h1" name="right_hip_pitch" joint="right_hip_pitch" forcerange="-200 200" kp="800"/>
    <position class="h1" name="right_knee" joint="right_knee" forcerange="-300 300" kp="800"/>
    <position class="h1" name="right_ankle" joint="right_ankle" forcerange="-40 40" kp="800"/>

    <position class="h1" name="torso" joint="torso" forcerange="-200 200" kp="400"/>

    <position class="h1" name="left_shoulder_pitch" joint="left_shoulder_pitch" forcerange="-40 40" kp="400"/>
    <position class="h1" name="left_shoulder_roll" joint="left_shoulder_roll" forcerange="-40 40" kp="400"/>
    <position class="h1" name="left_shoulder_yaw" joint="left_shoulder_yaw" forcerange="-18 18" kp="400"/>
    <position class="h1" name="left_elbow" joint="left_elbow" forcerange="-18 18" kp="400"/>

    <position class="h1" name="right_shoulder_pitch" joint="right_shoulder_pitch" forcerange="-40 40" kp="400"/>
    <position class="h1" name="right_shoulder_roll" joint="right_shoulder_roll" forcerange="-40 40" kp="400"/>
    <position class="h1" name="right_shoulder_yaw" joint="right_shoulder_yaw" forcerange="-18 18" kp="400"/>
    <position class="h1" name="right_elbow" joint="right_elbow" forcerange="-18 18" kp="400"/>
  </actuator>

  <sensor>
    <gyro site="imu" name="gyro"/>
    <velocimeter site="imu" name="local_linvel"/>
    <accelerometer site="imu" name="accelerometer"/>
    <framepos objtype="site" objname="imu" name="position"/>
    <framezaxis objtype="site" objname="imu" name="upvector"/>
    <framexaxis objtype="site" objname="imu" name="forwardvector"/>
    <framelinvel objtype="site" objname="imu" name="global_linvel"/>
    <frameangvel objtype="site" objname="imu" name="global_angvel"/>
    <framequat objtype="site" objname="imu" name="orientation"/>

    <framelinvel objtype="site" objname="right_foot" name="right_foot_global_linvel"/>
    <framelinvel objtype="site" objname="left_foot" name="left_foot_global_linvel"/>
  </sensor>
</mujoco>

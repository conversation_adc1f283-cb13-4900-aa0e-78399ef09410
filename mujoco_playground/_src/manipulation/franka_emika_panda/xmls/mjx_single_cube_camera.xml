<mujoco model="panda single cube camera">
  <include file="mjx_panda.xml"/>
  <!-- For now, we copy over mjx_scene.xml to ensure there's no extra light source -->
  <statistic center="0.3 0 0.4" extent="1"/>

  <option timestep="0.005" iterations="5" ls_iterations="8" integrator="implicitfast">
    <flag eulerdamp="disable"/>
  </option>

  <custom>
    <numeric data="12" name="max_contact_points"/>
  </custom>

  <visual>
    <headlight diffuse="0.6 0.6 0.6" ambient="0.3 0.3 0.3" specular="0 0 0"/>
    <rgba haze="0.15 0.25 0.35 1"/>
    <global azimuth="120" elevation="-20"/>
    <scale contactwidth="0.075" contactheight="0.025" forcewidth="0.05" com="0.05" framewidth="0.01" framelength="0.2"/>
  </visual>

  <asset>
    <texture type="skybox" builtin="gradient" rgb1="0.3 0.5 0.7" rgb2="0 0 0" width="512" height="3072"/>
    <texture type="2d" name="groundplane" builtin="checker" mark="edge" rgb1="0.2 0.3 0.4" rgb2="0.1 0.2 0.3"
      markrgb="0.8 0.8 0.8" width="300" height="300"/>
    <material class="panda" name="off_white0" rgba="1.0 1.0 1.0 1.0"/>
    <material class="panda" name="off_white1" rgba="0.9888888888888889 0.9888888888888889 0.9911111111111112 0.95"/>
    <material class="panda" name="off_white2" rgba="0.9777777777777777 0.9777777777777777 0.9822222222222222 0.9"/>
    <material class="panda" name="off_white3" rgba="0.9666666666666667 0.9666666666666667 0.9733333333333334 0.85"/>
    <material class="panda" name="off_white4" rgba="0.9555555555555556 0.9555555555555556 0.9644444444444444 0.8"/>
    <material class="panda" name="off_white5" rgba="0.9444444444444444 0.9444444444444444 0.9555555555555555 0.75"/>
    <material class="panda" name="off_white6" rgba="0.9333333333333333 0.9333333333333333 0.9466666666666667 0.7"/>
    <material class="panda" name="off_white7" rgba="0.9222222222222223 0.9222222222222223 0.9377777777777778 0.65"/>
    <material class="panda" name="off_white8" rgba="0.9111111111111111 0.9111111111111111 0.9288888888888889 0.6"/>
    <material class="panda" name="off_white9" rgba="0.9 0.9 0.92 0.55"/>
    <material name="groundplane" texture="groundplane" texuniform="true" texrepeat="5 5" reflectance="0.2"/>
  </asset>

  <worldbody>
    <!-- Matching the Realsense D435 -->
    <camera fovy="42" name="front" pos="1.176 0.001 0.483" xyaxes="0.006 1.000 -0.000 -0.720 0.004 0.694"/>
    <geom name="floor" size="0 0 0.05" type="plane" material="groundplane" contype="1"/>
    <geom name="init_space" size="0.02 1 0.01" type="plane" pos="0.631 0 0.001" rgba="1 1 1 1" contype="0" conaffinity="0"/>
    <body name="box" pos="1 0 0.03">
      <freejoint/>
      <geom type="box" name="box" size="0.02 0.02 0.03" condim="3"
       friction="1 .03 .003" rgba="1 0 0 1" contype="2" conaffinity="1" solref="0.01 1"/>
    </body>
    <body mocap="true" name="mocap_target">
      <geom type="box" size="0.02 0.02 0.03" rgba="1 0 0 0.2" contype="0" conaffinity="0"/>
    </body>
  </worldbody>

  <keyframe>
    <key name="low_home"
      qpos="-0.00002 0.47804 -0.00055 -1.81309 -0.00161 2.34597 0.78501 0.04000 0.04000 0.70000 0.00000 0.03000 1.00000 0.00000 0.00000 0.00000"
      ctrl="-0.00002 0.47804 -0.00055 -1.81309 -0.00161 2.34597 0.78501 0.04000"/>
    <key name="picked"
      qpos="0.0481 0.5604 -0.0417 -1.8459 0.0342 2.3654 0.7852 0.0198 0.0200 0.6368 0.0064 0.1179 1.0000 0.0001 -0.0010 -0.0063"
      ctrl="0.0481 0.5604 -0.0417 -1.8459 0.0342 2.3654 0.7852 0.0000"/>
  </keyframe>
</mujoco>

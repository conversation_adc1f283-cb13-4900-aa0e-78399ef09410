<mujoco model="panda cabinet">
  <include file="mjx_scene.xml"/>

  <worldbody>
    <camera name="side" pos="0.500 1.381 0.476" xyaxes="-0.990 0.139 0.000 0.000 0.000 1.000"/>
    <body name="handle" pos="0.5 0 0.5">
      <joint type="slide" axis="1 0 0" range="-0.5 0" limited="true" damping="0.1"/>
      <geom type="box" name="handle" size="0.01 0.08 0.01" condim="3"
       friction="1 .03 .003" rgba="0 1 0 1" contype="2" conaffinity="1" solref="0.01 1"/>
      <geom type="box" name="shelf" size="0.1 0.2 0.01" pos="0.03 0 0" zaxis="-1 0 0" condim="3"
       friction="1 .03 .003" rgba="0 1 0 0.2" contype="2" conaffinity="1" solref="0.01 1"/>
    </body>
    <body name="barrier" pos="0.520 0 0">
      <geom type="plane" name="barrier" pos="0 0 0" size="0.0 0.0 0.01" zaxis="-1 0 0" condim="3"
       friction="1 .03 .003" rgba="0 1 0 0.2" contype="2" conaffinity="1" solref="0.01 1" group="5"/>
    </body>
    <body mocap="true" name="mocap_target">
      <geom type="sphere" size="0.025" rgba="1 0 0 1" contype="0" conaffinity="0"/>
    </body>
  </worldbody>

  <keyframe>
    <key name="home"
      qpos="0 0.3 0 -1.57079 0 2.0 -0.7853 0.04 0.04 0.0"
      ctrl="0 0.3 0 -1.57079 0 2.0 -0.7853 0.04"/>
    <key name="upright"
      qpos="0 0 0 0 0 0 0 0 0 0.0"
      ctrl="0 0 0 0 0 0 0 0"/>
  </keyframe>
</mujoco>

<mujoco>
  <default>
    <default class="cube">
      <geom friction=".3 0.05" conaffinity="2" condim="3"/>
    </default>
  </default>

  <asset>
    <texture name="cube" type="cube" fileup="reorientation_cube_textures/fileup.png"
      fileback="reorientation_cube_textures/fileback.png" filedown="reorientation_cube_textures/filedown.png"
      filefront="reorientation_cube_textures/filefront.png" fileleft="reorientation_cube_textures/fileleft.png"
      fileright="reorientation_cube_textures/fileright.png"/>
    <material name="cube" texture="cube"/>
    <texture name="graycube" type="cube" fileup="reorientation_cube_textures/grayup.png"
      fileback="reorientation_cube_textures/grayback.png" filedown="reorientation_cube_textures/graydown.png"
      filefront="reorientation_cube_textures/grayfront.png" fileleft="reorientation_cube_textures/grayleft.png"
      fileright="reorientation_cube_textures/grayright.png"/>
    <material name="graycube" texture="graycube"/>
    <texture name="dexcube" type="2d" file="reorientation_cube_textures/dex_cube.png"/>
    <material name="dexcube" texture="dexcube"/>
    <mesh name="cube_mesh" file="meshes/dex_cube.obj" scale="0.035 0.035 0.035"/>
  </asset>

  <worldbody>
    <body name="cube" pos="0.11 0.0 0.1" quat="1 0 0 0" childclass="cube">
      <freejoint name="cube_freejoint"/>
      <geom type="mesh" mesh="cube_mesh" material="dexcube" contype="0" conaffinity="0" density="0" group="2"/>
      <geom name="cube" type="box" size=".035 .035 .035" mass=".108" group="3"/>
      <site name="cube_center" pos="0 0 0" group="4"/>
    </body>
  </worldbody>
</mujoco>
